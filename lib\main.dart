import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        scaffoldBackgroundColor: Colors.grey[300],
        textTheme: const TextTheme(
          displayLarge: TextStyle(
            fontSize: 48,
            color: Colors.red,
            fontWeight: FontWeight.w100,
          ),
          displayMedium: TextStyle(
            fontSize: 36,
            color: Colors.orange,
            fontWeight: FontWeight.w200,
          ),
          displaySmall: TextStyle(
            fontSize: 24,
            color: Colors.yellow,
            fontWeight: FontWeight.w300,
          ),
          bodyLarge: TextStyle(
            fontSize: 18,
            color: Colors.green,
            fontWeight: FontWeight.w400,
          ),
          bodyMedium: TextStyle(
            fontSize: 16,
            color: Colors.indigo,
            fontWeight: FontWeight.w500,
          ),
          bodySmall: TextStyle(
            fontSize: 14,
            color: Colors.deepPurple,
            fontWeight: FontWeight.w600,
          ),
        ),

        useMaterial3: true,
      ),

      //Add theme mode
      themeMode: ThemeMode.dark,

      //Make dark theme
      darkTheme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.red),
        scaffoldBackgroundColor: Colors.blueGrey[500],
        useMaterial3: true,
        textTheme: const TextTheme(
          displayLarge: TextStyle(
            fontSize: 48,
            color: Colors.deepPurple,
            fontWeight: FontWeight.w900,
          ),
          displayMedium: TextStyle(
            fontSize: 36,
            color: Colors.indigo,
            fontWeight: FontWeight.w800,
          ),
          displaySmall: TextStyle(
            fontSize: 24,
            color: Colors.green,
            fontWeight: FontWeight.w700,
          ),
          bodyLarge: TextStyle(
            fontSize: 18,
            color: Colors.yellow,
            fontWeight: FontWeight.w600,
          ),
          bodyMedium: TextStyle(
            fontSize: 16,
            color: Colors.orange,
            fontWeight: FontWeight.w500,
          ),
          bodySmall: TextStyle(
            fontSize: 14,
            color: Colors.red,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
      home: const MyHomePage(),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("IGME-340: Themes")),
      body: Column(
        children: [
          Container(
            width: 300,
            height: 200,
            color: Theme.of(context).colorScheme.primary,
            child: Text(
              "I am Green",
              style: Theme.of(context).textTheme.displayLarge,
            ),
          ),
          Container(
            width: 200,
            height: 200,
            color: Theme.of(context).colorScheme.secondary,
            child: Text(
              "I am Yellow",
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ),
          Container(
            width: 350,
            height: 100,
            color: Theme.of(context).colorScheme.error,
            child: Text(
              "I am Pink",
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
          ElevatedButton(
            onPressed: () {},
            child: const Text('Elevated Button'),
          ),
          ElevatedButton(
            onPressed: () {},
            child: const Text('Second Evelated Button'),
          ),
        ],
      ),
    );
  }
}
